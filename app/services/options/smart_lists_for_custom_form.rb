class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    if @params[:name] == 'Contributor'
      # For Contributors, we need to avoid N+1 queries by batch loading all data
      my_options << batch_load_contributor_names(options_ids)
    else
      list_options = @params[:name].constantize.where(id: options_ids)
      my_options << list_options.map { |option| { id: option.id, name: option.name } }
    end

    if @params[:name] == 'Contributor'
      related_item_options = linkables.select{ |item| ( item.linkable_type == 'CompanyUser' || item.linkable_type == 'Group' )}
      my_options << batch_load_contributor_ids(related_item_options)
    else
      related_item_options = linkables.select{ |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map{ |opt| { id: opt.linkable_id, name: opt.name } }
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      linkables_query = LinkableLink.joins(:source, :target)
                                    .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                    .map{ |item| item.target }
      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        @total_count = linkables_query.select { |item| item.linkable_type == @params[:name]}.length
      end
      linkables_query
    end

    def batch_load_contributor_ids(related_item_options)
      # Group by linkable_type for efficient batch loading
      company_user_items = related_item_options.select { |item| item.linkable_type == 'CompanyUser' }
      group_items = related_item_options.select { |item| item.linkable_type == 'Group' }

      contributor_map = {}

      # Batch load CompanyUsers with their contributors
      if company_user_items.any?
        company_user_ids = company_user_items.map(&:linkable_id)
        CompanyUser.where(id: company_user_ids).pluck(:id, :contributor_id).each do |id, contributor_id|
          contributor_map[id] = { type: 'CompanyUser', contributor_id: contributor_id }
        end
      end

      # Batch load Groups with their contributors
      if group_items.any?
        group_ids = group_items.map(&:linkable_id)
        Group.where(id: group_ids).pluck(:id, :contributor_id).each do |id, contributor_id|
          contributor_map[id] = { type: 'Group', contributor_id: contributor_id }
        end
      end

      # Map the results back to the original format
      related_item_options.map do |opt|
        contributor_info = contributor_map[opt.linkable_id]
        if contributor_info
          { id: contributor_info[:contributor_id], name: opt.name }
        else
          # Fallback (shouldn't happen normally)
          { id: nil, name: opt.name }
        end
      end
    end

    def batch_load_contributor_names(contributor_ids)
      # Batch load all contributor names without triggering the caching system
      # This completely eliminates N+1 queries for CompanyUser, Group, Guest, and User

      sql = <<-SQL
        SELECT
          c.id,
          CASE
            WHEN g.id IS NOT NULL THEN g.name
            WHEN cu.id IS NOT NULL THEN
              CASE
                WHEN u.first_name IS NOT NULL AND u.last_name IS NOT NULL
                THEN CONCAT(u.first_name, ' ', u.last_name)
                ELSE COALESCE(u.email, '')
              END
            WHEN guest.id IS NOT NULL THEN
              CASE
                WHEN guest.first_name IS NOT NULL AND guest.last_name IS NOT NULL
                THEN CONCAT(guest.first_name, ' ', guest.last_name)
                ELSE COALESCE(guest.email, '')
              END
            ELSE ''
          END as name
        FROM contributors c
        LEFT JOIN groups g ON g.contributor_id = c.id
        LEFT JOIN company_users cu ON cu.contributor_id = c.id
        LEFT JOIN users u ON u.id = cu.user_id
        LEFT JOIN guests guest ON guest.contributor_id = c.id
        WHERE c.id IN (#{contributor_ids.map { '?' }.join(',')})
      SQL

      results = ActiveRecord::Base.connection.exec_query(
        ActiveRecord::Base.send(:sanitize_sql_array, [sql] + contributor_ids)
      )

      results.map { |row| { id: row['id'], name: row['name'] } }
    end
end
