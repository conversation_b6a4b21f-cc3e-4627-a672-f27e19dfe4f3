class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    list_options = @params[:name].constantize.where(id: options_ids)
    my_options << list_options.map { |option| { id: option.id, name: option.name } }
    if @params[:name] == 'Contributor'
      related_item_options = linkables.select{ |item| ( item.linkable_type == 'CompanyUser' || item.linkable_type == 'Group' )}
      my_options << related_item_options.map{ |opt| { id: opt.linkable.contributor.id, name: opt.name } }
    else
      related_item_options = linkables.select{ |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map{ |opt| { id: opt.linkable_id, name: opt.name } }
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      # For Contributor queries, we need to preload the polymorphic associations
      if @params[:name] == 'Contributor'
        linkables_query = LinkableLink.joins(:source, :target)
                                      .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                      .includes(:target)
                                      .map{ |item| item.target }

        # Preload contributors for CompanyUser and Group linkables
        company_user_linkables = linkables_query.select { |l| l.linkable_type == 'CompanyUser' }
        group_linkables = linkables_query.select { |l| l.linkable_type == 'Group' }

        if company_user_linkables.any?
          company_user_ids = company_user_linkables.map(&:linkable_id)
          company_users = CompanyUser.includes(:contributor).where(id: company_user_ids).index_by(&:id)
          company_user_linkables.each { |l| l.linkable = company_users[l.linkable_id] }
        end

        if group_linkables.any?
          group_ids = group_linkables.map(&:linkable_id)
          groups = Group.includes(:contributor).where(id: group_ids).index_by(&:id)
          group_linkables.each { |l| l.linkable = groups[l.linkable_id] }
        end
      else
        linkables_query = LinkableLink.joins(:source, :target)
                                      .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                      .map{ |item| item.target }
      end

      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        @total_count = linkables_query.select { |item| item.linkable_type == @params[:name]}.length
      end
      linkables_query
    end
end
