class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    list_options = @params[:name].constantize.where(id: options_ids)
    my_options << list_options.map { |option| { id: option.id, name: option.name } }
    if @params[:name] == 'Contributor'
      related_item_options = linkables.select{ |item| ( item.linkable_type == 'CompanyUser' || item.linkable_type == 'Group' )}
      my_options << batch_load_contributor_options(related_item_options)
    else
      related_item_options = linkables.select{ |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map{ |opt| { id: opt.linkable_id, name: opt.name } }
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      linkables_query = LinkableLink.joins(:source)
                                    .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                    .map{ |item| item.target }
      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        @total_count = linkables_query.select { |item| item.linkable_type == @params[:name]}.length
      end
      linkables_query
    end

    def batch_load_contributor_options(related_item_options)
      # Group items by linkable_type to batch load efficiently
      company_user_items = related_item_options.select { |item| item.linkable_type == 'CompanyUser' }
      group_items = related_item_options.select { |item| item.linkable_type == 'Group' }

      contributor_map = {}

      # Batch load CompanyUsers with their contributors
      if company_user_items.any?
        company_user_ids = company_user_items.map(&:linkable_id)
        company_users = CompanyUser.includes(:contributor).where(id: company_user_ids)
        company_users.each do |cu|
          contributor_map[cu.id] = { type: 'CompanyUser', contributor_id: cu.contributor_id }
        end
      end

      # Batch load Groups with their contributors
      if group_items.any?
        group_ids = group_items.map(&:linkable_id)
        groups = Group.includes(:contributor).where(id: group_ids)
        groups.each do |group|
          contributor_map[group.id] = { type: 'Group', contributor_id: group.contributor_id }
        end
      end

      # Map the results back to the original format
      related_item_options.map do |opt|
        contributor_info = contributor_map[opt.linkable_id]
        if contributor_info
          { id: contributor_info[:contributor_id], name: opt.name }
        else
          # Fallback to original behavior if not found in batch (shouldn't happen normally)
          { id: opt.linkable_type.constantize.find(opt.linkable_id).contributor.id, name: opt.name }
        end
      end
    end
end
