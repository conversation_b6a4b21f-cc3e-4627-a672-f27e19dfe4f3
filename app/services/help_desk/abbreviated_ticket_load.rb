module HelpDesk
  class AbbreviatedTicketLoad
    include EveryoneGroupPermissionChecker
    include <PERSON>leCompanyCacheKeys

    attr_accessor :company_user, :ticket_ids, :ticket_columns, :default_ticket_columns

    def initialize(company_user, ticket_columns, default_ticket_columns, is_read_only = false)
      @is_read_only = is_read_only
      self.company_user = company_user
      self.ticket_columns = ticket_columns
      self.default_ticket_columns = default_ticket_columns
    end

    def call(limited_ticket_ids, sort_by)
      return [] unless limited_ticket_ids.present?
      @ticket_ids = limited_ticket_ids
      @first_time = true
      @expanded_form_fields_permission = []
      @form_field_permissions_everyone_group = {}
      @assigned_to_fields = []
      @contributors_data = {}
      field_permissions = {}
      @query_results = {}
      excluded_ticket_ids = []

      # populate tickets and permissions
      if ticket_ids.any?
        ticket_map
        assign_ticket_write_permissions
      end

      unless admin?
        set_user_data
        get_all_form_field_permissions
      end

      # Process custom form values for each ticket
      custom_form_values.find_each do |my_form_value|
        begin
          @form_value = my_form_value
          @form_field = nil
          @field_name = nil
          @field_type = nil
          @field_mapping_key = nil

          next if excluded_ticket_ids.include?(form_value.module_id)

          unless admin?
            # Fetch and apply permissions for non-admin users
            everyone_group = @form_field_permissions_everyone_group[form_field.id]
            permission_type = @expanded_form_fields_permission[form_field.id]&.permission_type
            if everyone_group.present? && permission_type.nil?
              # Assign "view" and "edit" permissions for the field
              field_permissions[form_field.id] = {
                can_view: everyone_group.can_view,
                can_edit: everyone_group.can_edit
              }
              can_view_map[form_field.id] ||= everyone_group.can_view
              can_edit_map[form_field.id] ||= everyone_group.can_edit
            else
              # Assign permissions based on specified permission type
              can_view_map[form_field.id] ||= permission_type == 'read'
              can_edit_map[form_field.id] ||= permission_type == 'write'
              permission_type = ""
            end
          end

          # Process field values based on field type
          if field_type == "tag" || field_type == "checkbox"
            # do nothing
          elsif smart_list_classes[field_type]
            ticket_value[:fields][field_mapping_key] ||= []
          else
            ticket_value[:fields][field_mapping_key] ||= {
              field_id: form_field.id,
              visible: is_field_visible?,
              disabled: is_field_disabled?,
              field_name: field_name,
              field_type: field_type,
            }
          end

          # Handle specific fields like status, priority, attachments
          if form_field.name == 'status' 
            next unless form_value.value_str
            set_status
          elsif form_field.name == 'priority'
            next unless form_value.value_str
            set_priority
          elsif form_field.singular?
            set_singular_field_type_value
          elsif field_type == "tag" || field_type == "checkbox"
            set_multi_list_field_type_value
          elsif field_type == "attachment"
            ticket_value[:fields][field_mapping_key][:value] ||= 0
            ticket_value[:fields][field_mapping_key][:value] += 1
          else
            next unless form_value.value_int || form_value.value_str
            set_smart_list_field
          end
        rescue => e
          Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
          excluded_ticket_ids.push(form_value.module_id)
          next
        end
      end

      # Remove excluded ticket IDs from the list of tickets
      @ticket_ids = @ticket_ids - excluded_ticket_ids if excluded_ticket_ids.any?

      # Then fill in the fields we want to batch load after iterating through the fields
      people_list_field_names
      populate_entities
      populate_comment_info
      populate_total_time_spent if ticket_column_names.include?(:total_time_spent)
      populate_due_at if ticket_column_names.include?(:due_at)
      @assigned_to_field_permissions = []

      # Map and update ticket data with permissions and assignments
      ticket_ids.map! do |id|
        if !admin? && ticket_values[id] && !ticket_values[id].fetch(:assigned_to, nil) && has_assigned_field(ticket_values, id)
          # Fetch permission details for the "assigned_to" field
          field_id = @assigned_to_fields[ticket_values[id][:custom_form_id]][:id]
          permission_type = @expanded_form_fields_permission[field_id]&.permission_type
          if field_permissions[field_id].present?
            permission = field_permissions[field_id]
            visible = permission[:can_view] || permission[:can_edit]
            disabled = !permission[:can_edit]
            permission = false
          elsif permission_type.present?
            visible = ['read', 'write'].include?(permission_type)
            disabled = permission_type != 'write'
            permission_type = ""
          elsif !field_permissions[field_id].present? && field_id.present?
            key = [name: 'field_permissions', field_id: field_id]
            @assigned_to_field_permissions = Rails.cache.fetch(key, :expires_in => 8.hours) do
              CustomFormFieldPermission.where(
                custom_form_field_id: field_id
              ).select(:id, :can_edit, :can_view, :contributor_id)
            end
            if is_everyone_group_permission?(@assigned_to_field_permissions)
              cffp = @assigned_to_field_permissions.first
              field_permissions[field_id] = {
                can_view: cffp.can_view,
                can_edit: cffp.can_edit
              }
              visible = cffp[:can_view] || cffp[:can_edit]
              disabled = !cffp[:can_edit]
              cffp = nil
            end
          end
          # Update ticket's "assigned_to" field with permissions
          ticket_values[id]['assigned_to'] = [{
            field_id: field_id,
            visible:  visible,
            disabled: disabled,
            field_name: 'assigned_to',
            field_type: 'people_list',
            manually_added: true,
          }]
        end
        ticket_values[id]
      end
      ticket_ids.compact
    end

    def ticket_map
      @ticket_map ||= HelpTicket.includes(:company, :workspace, :custom_form).where(id: ticket_ids).index_by(&:id)
    end

    def assign_ticket_write_permissions
      ticket_ids.each do |ticket_id|
        can_write = false
        ticket = ticket_map[ticket_id]
        if ticket.present?
          # Check if the company user is either an assignee or creator of the ticket
          can_write = ticket.assigned_user_ids.include?(company_user.contributor_id) || ticket.creator_ids.include?(company_user.contributor_id)
          # If the user doesn't have direct assignee/creator permissions, check group membership
          if !can_write && ticket.group_member_ids.present?
            ids = JSON.parse(ticket.group_member_ids)
            # Check if the user is assigned to the ticket or is the creator in the group
            can_write = ids["assigned_to"]&.include?(company_user.contributor_id) || ids["created_by"]&.include?(company_user.contributor_id)
          end
          @query_results[ticket_id] = can_write
        end
      end
    end

    def admin?
      if @first_time
        @admin ||= company_user.is_admin? || company_user.user.super_admin?
        @first_time = false
      end
      @admin
    end

    def set_user_data
      key = [name: 'companies_id', user_id: company_user.user_id]
      @company_ids = Rails.cache.fetch(key, :expires_in => 16.hours) do
        company_user.user.companies.pluck(:id)
      end

      @workspaces = Workspace.where(company_id: @company_ids)
    end

    def get_all_form_field_permissions
      # Getting all custom form IDs from which tickets were created.
      form_ids = custom_form_ids
      form_fields = CustomFormField.where(custom_form_id: form_ids).select(:id, :name, :field_attribute_type)
      form_ids = []
      # Fetching form field permissions for the 'Everyone' group, based on company ID and form field IDs
      @form_field_permissions_everyone_group = CustomFormFieldPermission.joins("INNER JOIN groups ON groups.contributor_id = custom_form_field_permissions.contributor_id")
                                                                        .where(groups: { name: "Everyone", company_id: @company_ids }, custom_form_field_id: form_fields.pluck(:id))
                                                                        .select(:custom_form_field_id, :can_edit, :can_view)
                                                                        .index_by(&:custom_form_field_id)
    
      # Getting assigned to field for those tickets that have no assignee.
      @assigned_to_fields = form_fields.where(
        name: 'assigned_to',
        field_attribute_type: 'people_list'
      ).select(:id, :custom_form_id).index_by(&:custom_form_id)

      # Getting all field permissions for all custom forms from which tickets were created.
      @expanded_form_fields_permission = ExpandedFormFieldPermission.where(
        contributor_id: company_user.contributor_id,
        custom_form_field_id: form_fields.pluck(:id)
      ).select(:id, :custom_form_field_id, :permission_type).index_by(&:custom_form_field_id)

      form_fields = []
    end

    # Retrieves custom form values for specific tickets based on column names.
    def custom_form_values
      @custom_form_values ||= CustomFormValue.
                                where(module_id: ticket_ids, module_type: "HelpTicket").
                                joins(:custom_form_field).
                                where(custom_form_fields: { name: ticket_column_names }).
                                select("id",
                                       "custom_form_field_id",
                                       "module_id",
                                       "value_int",
                                       "value_str")
    end

    def ticket_column_names
      @ticket_column_names ||= ticket_columns.map { |c| c["field_name"].to_sym }
    end

    def has_assigned_field(ticket_values, ticket_id)
      @assigned_to_fields[ticket_values[ticket_id][:custom_form_id]] && @assigned_to_fields[ticket_values[ticket_id][:custom_form_id]][:id].present? 
    end

    def form_value
      @form_value
    end

    def form_field
      field_map[form_value.custom_form_field_id]
    end

    def field_map
      @field_map ||= CustomFormField.where(custom_form_id: custom_form_ids).index_by(&:id)
    end

    def custom_form_ids
      @custom_form_ids ||= ticket_map.values.pluck(:custom_form_id).uniq
    end

    def can_view 
      can_view_map[form_field.id]
    end

    def can_view_map
      @can_view_map ||= {}
    end

    def can_edit
      can_edit_map[form_field.id]
    end

    def can_edit_map
      @can_edit_map ||= {}
    end

    def field_type
      @field_type ||= form_field.field_attribute_type
    end

    def smart_list_classes
      @smart_list_classes ||= {
        asset_list: ManagedAsset,
        contract_list: Contract,
        vendor_list: Vendor,
        telecom_list: TelecomService,
        people_list: Contributor,
        location_list: Location,
      }.with_indifferent_access
    end

    def ticket_value
      current_ticket = ticket || form_value.help_ticket
      ticket_values[form_value.module_id] ||= {
        id: form_value.module_id,
        ticket_number: current_ticket.ticket_number,
        comment_count: 0,
        resolution_time: resolution_time(current_ticket),
        is_closed: current_ticket.closed?,
        updated_at: current_ticket.updated_at,
        custom_form_id: current_ticket.custom_form_id,
        created_at: current_ticket.created_at,
        source: current_ticket.source,
        total_time_spent: "0 hr 0 min", 
        archived: current_ticket.archived,
        workspace_id: current_ticket.workspace_id,
        workspace: current_ticket.workspace.name,
        form_used: current_ticket.custom_form.form_name, 
        company: company_data(current_ticket),
        can_write: can_write?(current_ticket),
        unread_comments_count: current_ticket.unread_comments_count,
        is_new: current_ticket.is_new,
        is_seen: current_ticket.is_seen,
        closed_at: current_ticket.closed_at,
        fields: {}     
      }
    end

    def ticket
      ticket_map[form_value.module_id]
    end

    def ticket_values
      @ticket_values ||= {}
    end

    def resolution_time(ticket)
      ticket&.sla_response&.dig("resolution_target_time")&.in_time_zone(ticket&.company&.timezone || "America/Chicago")
    end

    def company_data(current_ticket)
      company = current_ticket.company
      { id: company.id, name: company.name }
    end

    # Checks if the current user has write or specific read permissions for the given ticket based on workspace and ticket specific privileges
    def can_write?(current_ticket)
      return false if @is_read_only

      return true if admin?

      @workspaces_privilege ||= {}
      privilege ||= nil
      result ||= nil

      if is_multi_companies_or_workspaces?
        @contributor_id = ticket_user_contributor_id(current_ticket)
        privilege = check_workspace_permission(current_ticket)
      else
        @contributor_id ||= ticket_user_contributor_id(current_ticket)
        privilege ||= check_workspace_permission(current_ticket)
      end

      permission_type = privilege.present? ? privilege : ticket_privilege(current_ticket)
    
      if permission_type == 'write'
        return true
      elsif ['readscoped', 'scoped', 'basicread'].include?(permission_type)
        if is_multi_companies_or_workspaces?
          result = get_specific_ticket_permission(current_ticket)
        else
          result ||= get_specific_ticket_permission(current_ticket)
        end
        return result
      end
      return false
    end

    def is_multi_companies_or_workspaces?
      multipl_companies_workspaces ||= @company_ids.count > 1 || @workspaces.count > 1
    end

    def ticket_user_contributor_id(current_ticket)
      if current_ticket.company_id == company_user.company_id
        company_user.contributor_id
      else
        # If the companies don't match, find the contributor_id from the user email and current ticket's company users
        company_user_cache(current_ticket.company, company_user.email)&.contributor_id
      end
    end

    def check_workspace_permission(current_ticket)
      @workspaces_privilege[current_ticket.workspace_id]
    end

    def ticket_privilege(current_ticket)
      permission_type = ExpandedPrivilege.find_by(workspace_id: current_ticket.workspace_id,
                                                  company_id: current_ticket.company_id,
                                                  contributor_id: @contributor_id)&.permission_type
      @workspaces_privilege[current_ticket.workspace_id] = permission_type
      permission_type
    end

    def get_specific_ticket_permission(current_ticket)
      @query_results[current_ticket.id]
    end

    def field_mapping_key
      @field_mapping_key ||= unique_field_name
    end

    def unique_field_name
      field_name = form_field.name.to_sym
      return field_name if default_ticket_columns.include?(form_field.name)

      # Find the column matching both the field name and description
      col = ticket_columns.find { |f| f["field_name"] == form_field.name && f["description"] == form_field.label }
      return field_name if col.present?
      
      # Find the column matching field type and description
      label = ticket_columns.find { |f| f["field_type"] == form_field.field_attribute_type && f["description"] == form_field.label }
      # Return a unique label if found, else return the original field name
      label.present? ? form_field.label.downcase.gsub(' ', '_').to_sym : field_name
    end

    def is_field_visible?
      can_view || can_edit || admin?
    end
    
    def is_field_disabled?
      !(can_edit || admin?)
    end

    def field_name
      @field_name ||= form_field.name.to_sym
    end

    def set_status
      options = (field_options[form_field.id] ||= JSON.parse(form_field.options))
      value = form_value.value_str
      ticket_value[:fields][field_mapping_key].merge!(
        value: value,
        color: options.find { |o| o['name'] == value }['color']
      )
    end

    def set_priority
      options = (field_options[form_field.id] ||= JSON.parse(form_field.options))
      value = form_value.value_str
      ticket_value[:fields][field_mapping_key].merge!(
        value: value.downcase,
        color: options.find { |o| o['name'].downcase == value }['color']
      )
    end

    def field_options
      @field_options ||= {}
    end

    def set_singular_field_type_value
      ticket_value[:fields][field_mapping_key].merge!(
        value: form_value.value_str
      )
    end

    def set_multi_list_field_type_value
      # If the field already exists, append the new value to the existing one
      if ticket_value[:fields][field_mapping_key]
        ticket_value[:fields][field_mapping_key][:value] += ",#{form_value.value_str}"
      else
        # If the field does not exist, initialize it with the field type and set its value
        ticket_value[:fields][field_mapping_key] ||= {
          field_type: field_type,
        }
        ticket_value[:fields][field_mapping_key][:value] = form_value.value_str
      end
    end

    def set_smart_list_field
      staff = @contributors_data[form_value.value_int]
      value = {}
      user_list_class = ['people_list']

      if staff.present?
        cont_staff = staff[:staff_member]
        staff = nil
      else
        contributor = Contributor.includes(:company_user, :group, :guest).find_by(id: form_value.value_int) if field_type == 'people_list'
        cont_staff = contributor&.contributor_staff(field_name)
        @contributors_data[contributor&.id] = {
          staff_member: cont_staff
        }
        contributor = nil
      end

      smart_list_class = smart_list_classes[field_type]
      
      # Build value hash for the smart list field, based on availability of staff and field type
      if cont_staff.present? || field_type != 'people_list'
        value = {
          field_id: form_field.id,
          field_name: form_field.name,
          field_type: field_type,
        }
        # Set visibility and disabled status based on current field value or defaults
        value[:visible] = ticket_value[:fields][field_mapping_key].empty? ? is_field_visible? : ticket_value[:fields][field_mapping_key][0][:visible]
        value[:disabled] = ticket_value[:fields][field_mapping_key].empty? ? is_field_disabled? : ticket_value[:fields][field_mapping_key][0][:disabled]
        value[:id] = form_value.value_int
        entities_to_lookup[smart_list_class] ||= {}
        entities_to_lookup[smart_list_class][form_value.value_int] ||= []
      end
      if form_value.value_int
        if field_type == 'people_list'
          entities_to_lookup[smart_list_class][form_value.value_int] << value if cont_staff.present?
        else
          entities_to_lookup[smart_list_class][form_value.value_int] << value
        end
      end
      # If the field type is a people list, add the avatar URL to the value if available
      if user_list_class.include?(form_field.field_attribute_type)
        val = cont_staff.class == CompanyMember ? cont_staff&.avatar_url : nil
        value[:avatar] = val if val.present?
      end
      form_field_classes[form_field.id] ||= smart_list_class
      # Add the value to the ticket fields if staff is present or the field type is not people list
      ticket_value[:fields][field_mapping_key] << value if cont_staff.present? || field_type != 'people_list'
    end

    def entities_to_lookup
      @entities_to_lookup ||= {} # entities that were looked up
    end

    def form_field_classes
      @form_field_classes ||= {} # map form field id to class
    end

    def people_list_field_names
      @people_list_field_names ||= field_map.values.select { |field| field.field_attribute_type == "people_list" }&.pluck(:name)&.map(&:to_sym)
    end

    #Populates entity names into ticket values and sorts people list fields
    def populate_entities
      # Ok, now that we have all of the values populated, we can do our lookups for the different entities.
      entities_to_lookup.each_pair do |entity_class, entity_map|
        # Fetch entities with associated data based on the entity class type.
        if entity_class == Contributor
          my_entities = entity_class.where(id: entity_map.keys).includes(company_user: :user)
        elsif entity_class == Location
          my_entities = entity_class.where(id: entity_map.keys).includes(custom_form_values: :custom_form_field)
        else
          my_entities = entity_class.where(id: entity_map.keys)
        end

        # Assign the entity name to each associated value in the entity map.
        my_entities.find_each do |entity|
          name = entity.name
          entity_map[entity.id].each do |v|
            v[:name] = name
          end
        end
        my_entities = []
      end

      # Sort the values of people list fields alphabetically by name for better readability
      ticket_values.values.each do |value|
        people_list_field_names.each do |field|
          value[field] = value[field].sort { |a, b| (a[:name].downcase  <=>  b[:name].downcase  ) } if value[field].present?
        end
      end
    end

    # Fetches and updates tickets with their non-private comment count and the timestamp of the last comment.
    def populate_comment_info
      sql = """
        SELECT htc.help_ticket_id, max(htc.created_at) AS last_comment_at, count(htc.id) AS comment_count
          FROM help_ticket_comments AS htc
        LEFT OUTER JOIN time_spents AS ts
          ON ts.help_ticket_id IN (?)
            AND ts.help_ticket_comment_id = htc.id
        WHERE htc.help_ticket_id IN (?) AND htc.private_flag = FALSE AND ts.help_ticket_comment_id IS NULL
        GROUP BY htc.help_ticket_id
      """
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, ticket_ids, ticket_ids])
      results = ActiveRecord::Base.connection.execute(query)

      # Iterate through the query results and populate the ticket values with comment data.
      results.each do |r|
        ticket_values[r['help_ticket_id']].merge!(
          comment_count: r['comment_count'], # Total number of non-private comments without time spent entries.
          last_comment_at: r['last_comment_at'] # Timestamp of the most recent non-private comment.
        )
      end
    end

    # Calculates and updates total time spent for each help ticket using aggregated time entries.
    def populate_total_time_spent
      sql = """
        SELECT help_ticket_id, sum(hours_spent) as hours_spent, sum(minutes_spent) as minutes_spent
          FROM time_spents
        WHERE help_ticket_id IN (?) 
        GROUP BY help_ticket_id
      """
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, ticket_ids])
      results = ActiveRecord::Base.connection.execute(query)

      # Iterate through the query results and populate the ticket values with total time spent.
      results.each do |r|
        ticket_values[r['help_ticket_id']][:total_time_spent] = HelpDesk::TimeSpentFormat.format(r["hours_spent"] || 0, r["minutes_spent"] || 0)
      end
    end

    def populate_due_at
      sql = """
        SELECT help_ticket_id, min(due_at) due_at
          FROM project_tasks
        WHERE help_ticket_id IN (?) AND completed_at IS NULL
        GROUP BY help_ticket_id
      """
      query = ActiveRecord::Base.send(:sanitize_sql, [sql, ticket_ids])
      results = ActiveRecord::Base.connection.execute(query)
      results.each do |r|
        ticket_values[r['help_ticket_id']][:due_at] = r["due_at"]
      end
    end

    def comment_count
      ticket.help_ticket_comments.select {|c| !c.private_user_flag }.size
    end

    def includes_field_name?(field_name)
      ticket_column_names.include?(field_name)
    end

    def ticket_ids
      @ticket_ids
    end

    def color_map
      @color_map ||= {}
    end

    def entities
      @entities ||= {}
    end

    def avatar_map
      @avatar_map ||= {}
    end
  end
end
