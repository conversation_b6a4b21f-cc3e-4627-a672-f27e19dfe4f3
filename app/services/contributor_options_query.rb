class ContributorOptionsQuery
  attr_accessor :params

  def initialize(params)
    @params = params
  end

  def call
    # Include all associations to prevent N+1 queries
    options = contributors.includes(:group, :company_user, :guest)
    options = apply_query(options) if query.present?

    options = filter_archived(options)
    options = options.where(id: includes) if includes.present?
    options = options.where.not(id: excludes) if excludes.present?
    options = options.limit(limit) if limit
    options = options.offset(offset) if offset
    options.order(sort_order_sql)
  end

  private

  def apply_query(options)
    sql_query, sql_params = sql_query_and_params
    options.where(sql_query, *sql_params)
  end

  def include_guests?
    params[:include_guests]
  end

  def excludes
    params[:excludes]
  end

  def includes
    params[:includes]
  end

  def archived
    params[:archived]
  end

  def offset
    params[:offset]
  end

  def limit
    params[:limit]
  end

  def query
    params[:query]
  end

  def values
    params[:values]
  end

  def contributors
    params[:contributors]
  end

  def sort_preferences
    params[:sort_preferences]
  end

  def sort_list_order
    return "users.first_name, users.last_name" unless sort_preferences.present?

    sort_order = sort_preferences.include?('Ascending') ? 'ASC' : 'DESC'
    if sort_preferences.include?("Last Name, First Name")
      "users.last_name #{sort_order}, users.first_name #{sort_order}"
    else
      "users.first_name #{sort_order}, users.last_name #{sort_order}"
    end
  end

  def sql_query_and_params
    sql = 'users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR groups.name ILIKE ?'
    sql += ' OR guests.last_name ILIKE ? OR guests.email ILIKE ?' if include_guests?

    queries = []
    sql_param_count = sql.scan(/\?/).length
    sql_params = []
    query_params.each do |p|
      sql_param_count.times { sql_params << p }
      queries << "(#{sql})"
    end

    [queries.join(" AND "), sql_params]
  end

  def query_params
    query.split.map { |q| "%#{q}%" }
  end

  def filter_archived(options)
    return options if archived.blank? || archived == 'all'

    if archived == 'true' && values.present?
      options.where("(archived = ? OR id IN (?))", true, values)
    elsif archived == 'true'
      options.where("archived = ?", true)
    elsif values.present?
      options.where("id IN (?)", values)
    else
      options
    end
  end

  def sort_order_sql
    return sort_list_order unless include_guests?

    "groups.name, #{sort_list_order}, guests.first_name, guests.last_name"
  end
end
