class Contributor < ApplicationRecord
  include DeleteFormValue
  include HandleCompanyCacheKeys
  include CompanyCache

  belongs_to :company

  has_many :custom_form_field_permissions, dependent: :destroy
  has_one :company_user, dependent: :nullify
  has_one :group, dependent: :nullify
  has_one :guest, dependent: :nullify

  has_many :task_assignees, dependent: :destroy
  has_many :project_tasks, dependent: :nullify
  has_many :privileges, dependent: :destroy
  has_many :group_members, dependent: :destroy
  has_many :automated_tasks, class_name: 'AutomatedTasks::AutomatedTask', dependent: :nullify
  has_many :assets_automated_tasks, class_name: 'Assets::AutomatedTask', dependent: :nullify
  has_many :contributor_actionable_alerts, dependent: :destroy
  has_many :managed_assignments, class_name: :AssignmentInformation, foreign_key: :managed_by_contributor_id, dependent: :nullify
  has_many :agent_locations, class_name: :AgentLocation, foreign_key: :managed_by_contributor_id, dependent: :nullify
  has_many :probe_locations, class_name: :ProbeLocation, foreign_key: :managed_by_contributor_id, dependent: :nullify
  has_many :used_assignments, class_name: :AssignmentInformation, foreign_key: :used_by_contributor_id, dependent: :nullify
  has_many :history_managed_assignments, class_name: :AssetUsageHistory, foreign_key: :managed_by_contributor_id, dependent: :nullify
  has_many :history_used_assignments, class_name: :AssetUsageHistory, foreign_key: :used_by_contributor_id, dependent: :nullify
  has_many :expanded_privileges, dependent: :destroy
  has_many :article_privileges, dependent: :destroy
  has_many :article_expanded_privileges, dependent: :destroy
  has_many :module_alert_contributors
  has_many :module_alerts, through: :module_alert_contributors

  has_many :created_managed_assets, -> { unscope(where: :merged) }, class_name: 'ManagedAsset', foreign_key: 'creator_id', dependent: :nullify
  has_many :created_contracts, class_name: 'Contract', foreign_key: 'creator_id', dependent: :nullify
  has_many :created_telecom_services, class_name: "TelecomService",  foreign_key: 'creator_id', dependent: :nullify
  has_many :created_telecom_providers, class_name: "TelecomProvider",  foreign_key: 'creator_id', dependent: :nullify
  has_many :created_vendors, class_name: "Vendor", foreign_key: "creator_id", dependent: :nullify
  has_many :expanded_form_field_permissions, dependent: :destroy
  has_many :responses, class_name: 'CustomSurvey::Response', dependent: :destroy

  before_destroy :remove_assigned_user_ids
  after_destroy :delete_options_cache_keys
  after_destroy :delete_contributor_company_user_cache_keys, if: -> { contributor_type == 'CompanyUser' }
  after_destroy -> { delete_company_user_find_cache_keys(["company_user_find_by_{:contributor_id=>#{self.id}}"]) }, if: -> { contributor_type == 'CompanyUser' }
  after_destroy :delete_contributor_cache_keys, if: -> { contributor_type == 'Group' }
  after_destroy :delete_contributor_find_cache_keys

  scope :find_users_by_email, ->(email) { left_joins(:guest, company_user: :user).where("users.email = ? OR guests.email = ?", email, email) }

  def name
    if contributor_group
      contributor_group.name
    elsif contributor_company_user
      contributor_company_user.full_name || contributor_company_user.email
    elsif guest
      guest.full_name || guest.email
    else
      nil
    end
  end

  def linkable_id
    if contributor_group
      contributor_group.linkable_id
    else
      contributor_company_user.linkable_id
    end
  end

  def first_name
    contributor_group ? contributor_group.name : contributor_company_user&.first_name
  end

  def last_name
    contributor_group ? contributor_group.name : contributor_company_user&.last_name
  end

  def email
    contributor_group ? "Group" : contributor_company_user&.email || guest&.email
  end

  def avatar
    contributor_group ? nil : avatar_thumb_url
  end

  def phone
    contributor_group ? nil : contributor_company_user&.mobile_phone || "Not Provided"
  end

  def root_id
    if contributor_group
      contributor_group.id
    elsif contributor_company_user
      contributor_company_user.id
    else
      guest.id
    end
  end

  def contributor_type
    if contributor_group
      'Group'
    elsif contributor_company_user
      'CompanyUser'
    else
      'Guest'
    end
  end

  def avatar_thumb_url
    return nil unless contributor_company_user&.user_avatar&.attachment.present?
    contributor_company_user&.user_avatar.as_json[:attachment][:attachment_thumb_url]
  end

  def contributor_ids_all(visited = [])
    return [] if visited.flatten.include?(self.id)
    my_list = []
    my_list << self.id
    visited << self.id
    if contributor_group
      my_list += company.company_users.pluck(:contributor_id) if contributor_group.include_all
      contributor_group.group_members.each do |member|
        my_list += member.contributor.contributor_ids_all(visited)
      end
    end
    my_list.flatten.uniq
  end

  def contains?(id)
    return true if id == self.id
    if contributor_group
      return true if contributor_group.include_all
      return true if contributor_group.group_members.where(contributor_id: id).exists?
      group_contributor = find_contributor_by_id(id)
      contributor_group.group_members.joins(contributor: :group).includes(contributor: :group).find_each do |member|
        return true if member.contributor.group.contains?(group_contributor)
      end
    end
    false
  end

  def contributor_company_user
    if is_cache_enabled?("contributor_company_user")
      key = "contributor_company_user_#{self.id}"
      Rails.cache.fetch(key, :expires_in => 8.hours) do
        company_user
      end
    else
      company_user
    end
  end

  def contributor_group
    if is_cache_enabled?("contributor_type_group")
      key = "contributor_group_#{self.id}"
      Rails.cache.fetch(key, :expires_in => 8.hours) do
        group
      end
    else
      group
    end
  end

  def contributor_ids_only_users(visited = [])
    return [self.id] if contributor_company_user || guest

    sql = """
      WITH RECURSIVE contributor_hierarchy AS (
        SELECT c2.id AS contributor_id, ARRAY[]::bigint[] AS visited_ids
        FROM contributors c
        JOIN groups g ON g.contributor_id = c.id
        JOIN group_members gm ON gm.group_id = g.id
        JOIN contributors c2 ON c2.id = gm.contributor_id
        WHERE c.id = :contributor_id

        UNION ALL

        SELECT c.id, ch.visited_ids || ch.contributor_id
        FROM contributors c
        JOIN group_members gm ON gm.contributor_id = c.id
        JOIN groups g ON g.id = gm.group_id
        JOIN contributor_hierarchy ch ON g.contributor_id = ch.contributor_id
        WHERE NOT c.id = ANY(ch.visited_ids)
      )
      SELECT ch.contributor_id
      FROM contributor_hierarchy ch
      WHERE ch.contributor_id NOT IN (
        SELECT unnest(visited_ids) FROM contributor_hierarchy
      )
    """

    results = ActiveRecord::Base.connection.execute(
      ActiveRecord::Base.send(:sanitize_sql_array, [sql, { contributor_id: self.id }])
    )

    results.values.flatten.uniq
  end

  def company_users
    CompanyUser.where(contributor_id: contributor_ids_only_users)
  end

  def remove_assigned_user_ids
    self.company.help_tickets.where("assigned_user_ids @> ARRAY[?]", self.id).update_all("assigned_user_ids = array_remove(assigned_user_ids, #{self.id})")
  end

  def group_members_ids
    contributor_group ? contributor_group.contributors.pluck(:id) : []
  end

  def as_json_abbreviated
    {
      id: id,
      name: name,
      avatar: avatar,
      company_user_id: contributor_company_user&.id,
      company_group_id: contributor_group&.id,
      group_members_ids: group_members_ids
    }
  end

  def field_type
    CustomForms::FieldType.new(self.class.name).call
  end

  def contributor_staff(field_name = nil)
    # Use preloaded associations instead of individual find_by queries
    staff = case contributor_type
            when 'CompanyUser'
              company_user
            when 'Group'
              group
            when 'Guest'
              guest
            end
    return nil if archived_staff_field(field_name, staff)
    staff
  end

  def archived_staff_field(field_name, staff)
    ![:created_by, :assigned_to].include?(field_name) &&
    self.contributor_type == "CompanyUser" && !staff.archived_at.nil?
  end
end
