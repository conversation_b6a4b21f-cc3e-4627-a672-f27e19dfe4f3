class CreateSampleCompanyThirdWorker
  include Sidekiq::Job
  include CreateSampleCompanyHelper
  sidekiq_options queue: 'high_intensity_schedule'

  def perform(this_worker_only = false)
    @errors = []
    @errors_details = []
    delete_data if this_worker_only

    begin
      managed_assets
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @managed_asset_related_info
    end

    begin
      discovered_assets
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @managed_asset_related_info
    end

    begin
      agent_locations
      probe_locations
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @managed_asset_related_info
    end

    begin
      discovered_users
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @managed_asset_related_info
    end

    begin
      meraki_integration
      ubiquiti_integration
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @managed_asset_related_info
    end

    CreateSampleCompanyFourthWorker.perform_async unless this_worker_only
    raise (@errors + @errors_details).join(", ") if @errors.any?
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end

  def delete_data
    sample_company.managed_assets&.destroy_all
    sample_company.discovered_assets&.destroy_all
    sample_company.agent_locations&.destroy_all
    sample_company.probe_locations&.destroy_all
    sample_company.discovered_users&.destroy_all
    sample_company.meraki_config&.destroy
    sample_company.ubiquiti_config&.destroy
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end

  def managed_assets
    @managed_assets = {}
    parent_company.managed_assets.each do |managed_asset|
      @managed_asset_related_info = { managed_asset_id: managed_asset.id }
      asset = managed_asset.dup
      asset.hardware_detail = asset.asset_type.hardware_detail_class.new
      if managed_asset.hardware_detail
        attrs = managed_asset.hardware_detail.attributes
        attrs.delete("id")
        attrs.delete("updated_at")
        attrs.delete("created_at")
        asset.hardware_detail.assign_attributes(attrs)
      end
      asset.guid = nil
      asset.agent_location_id = nil

      managed_asset.managed_asset_tags.each do |tag|
        new_tag = tag.dup
        new_tag.company_id = sample_company.id
        asset.managed_asset_tags << new_tag
      end

      managed_asset.asset_softwares.each do |asset_software|
        asset.asset_softwares << asset_software.dup
      end

      if managed_asset.discovered_asset
        discovered_asset_dup = managed_asset.discovered_asset.dup
        discovered_asset_dup.managed_asset_id = nil
        discovered_asset_dup.company_id = sample_company.id
        asset.discovered_asset = discovered_asset_dup
      end

      managed_asset.cloud_asset_attributes.each do |attribute|
        attribute_dup = attribute.dup
        attribute_dup.discovered_asset = asset.discovered_asset
        asset.cloud_asset_attributes << attribute_dup
      end

      managed_asset.asset_sources.each do |asset_source|
        source_dup = asset_source.dup
        source_dup.discovered_asset_id = nil
        source_dup.managed_asset_id = nil
        if ['manually_added', 'uploaded'].include?(source_dup.source)
          asset.asset_sources << source_dup
        else
          asset.discovered_asset.asset_sources << source_dup
          asset.asset_sources << source_dup
        end
      end

      asset.cost = managed_asset.cost.dup if managed_asset.cost
      if managed_asset.assignment_information
        asset.assignment_information = managed_asset.assignment_information.dup

        if managed_asset.assignment_information.location.present?
          name = asset.assignment_information.location.name
          asset.assignment_information.location = find_location(name)
        end

        manager = managed_asset.assignment_information.manager
        if manager
          manager_with_includes = Contributor.includes(:company_user, :group, :guest).find_by(id: manager.id)
          asset.assignment_information.manager = find_contributor(manager_with_includes&.contributor_staff).contributor
        end
        user = managed_asset.assignment_information.user
        if user
          user_with_includes = Contributor.includes(:company_user, :group, :guest).find_by(id: user.id)
          asset.assignment_information.user = find_contributor(user_with_includes&.contributor_staff).contributor
        end
      end

      asset.company_id = sample_company.id
      asset.asset_type = sample_company.asset_types.find_by(name: managed_asset.asset_type.name) if managed_asset.asset_type
      asset.asset_type = sample_company.asset_types.first if asset.asset_type.nil?
      asset.location = find_location(managed_asset.location.name) if managed_asset.location
      asset.vendor = find_vendor(managed_asset.vendor.name) if managed_asset.vendor
      @managed_assets[asset.name] = asset
      asset.created_by = admin_company_user
      asset.save(validate: false)
    end
  end

  def discovered_assets
    @discovered_assets ||= begin
      @discovered_assets = {}

      parent_company.discovered_assets.where(managed_asset_id: nil).each do |discovered_asset|
        @managed_asset_related_info = { discovered_asset_id: discovered_asset.id }
        discovered_asset_dup = discovered_asset.dup
        discovered_asset_dup.company_id = nil

        discovered_asset.asset_softwares.each do |asset_software|
          discovered_asset_dup.asset_softwares << asset_software.dup
        end

        discovered_asset_dup.integrations_locations_id = sample_company.integrations_locations.where(address: discovered_asset.integration_location&.address,
                                                                                                     source: discovered_asset[:source]
                                                                                          )&.first&.id

        discovered_asset.asset_sources.each do |asset_source|
          source_dup = asset_source.dup
          source_dup.discovered_asset_id = nil
          discovered_asset_dup.asset_sources << source_dup
        end

        discovered_asset.cloud_asset_attributes.each do |attribute|
          attribute_dup = attribute.dup
          attribute_dup.discovered_asset_id = nil
          attribute_dup.managed_asset_id = nil
          discovered_asset_dup.cloud_asset_attributes << attribute_dup
        end

        sample_company.discovered_assets << discovered_asset_dup
      end
      @discovered_assets
    end
  end

  def agent_locations
    parent_company.agent_locations.each do |agent_location|
      @managed_asset_related_info = { agent_location_id: agent_location.id }
      pl = agent_location.dup
      pl.company_id = nil
      sample_company.agent_locations << pl
    end
    sample_company.save!
  end

  def probe_locations
    parent_company.probe_locations.each do |probe_location|
      @managed_asset_related_info = { probe_location_id: probe_location.id }
      pl = probe_location.dup
      pl.company_id = nil
      sample_company.probe_locations << pl
    end
    sample_company.save!
  end

  def discovered_users
    @discovered_users ||= begin
      @discovered_users = {}

      parent_company.discovered_users.each do |discovered_user|
        @managed_asset_related_info = { discovered_user_id: discovered_user.id }
        discovered_user_dup = discovered_user.dup
        discovered_user_dup.company_id = nil
        sample_company.discovered_users << discovered_user_dup
      end
      @discovered_users
    end
  end

  def meraki_integration
    if parent_company.meraki_config.present?
      @managed_asset_related_info = { meraki_config: parent_company.meraki_config.id }
      meraki_conf = parent_company.meraki_config

      meraki_dup = meraki_conf.dup
      meraki_dup.assign_attributes(skip_callbacks: true, company: sample_company)
      meraki_dup.save!

      meraki_comp_integ = meraki_conf.company_integration
      
      meraki_comp_intg_dup = meraki_comp_integ.dup
      meraki_comp_intg_dup.assign_attributes(company_id: sample_company.id, integrable: meraki_dup)
      meraki_comp_intg_dup.save!
      meraki_dup.company_integration = meraki_comp_intg_dup
    end
  end

  def ubiquiti_integration
    if parent_company.ubiquiti_config.present?
      @managed_asset_related_info = { ubiquiti_config: parent_company.ubiquiti_config.id }
      ubiquiti_conf = parent_company.ubiquiti_config
      ubiquiti_controllers = ubiquiti_conf.ubiquiti_controllers

      ubiquiti_dup = ubiquiti_conf.dup

      ubiquiti_controllers.each do |ubq_controller|
        ubiquiti_dup.ubiquiti_controllers << ubq_controller.dup
      end

      ubiquiti_dup.assign_attributes(skip_callbacks: true, company: sample_company)
      ubiquiti_dup.save!
      ubiquiti_comp_integ = ubiquiti_conf.company_integration

      ubiquiti_comp_intg_dup = ubiquiti_comp_integ.dup
      ubiquiti_comp_intg_dup.assign_attributes(company_id: sample_company.id, integrable: ubiquiti_dup)
      ubiquiti_comp_intg_dup.save!
      ubiquiti_dup.company_integration = ubiquiti_comp_intg_dup
    end
  end
end
