class CreateSampleCompanyFourthWorker
  include Sidekiq::Job
  include CreateSampleCompanyHelper
  include HandleCompanyCacheKeys

  sidekiq_options queue: 'high_intensity_schedule'

  def perform(this_worker_only = false)
    @errors = []
    @errors_details = []
    delete_data if this_worker_only

    begin
      ticket_emails
      help_ticket_library_documents
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @help_desk_related_info
    end

    begin
      snippets
      articles
      helpdesk_faqs
      helpdesk_settings
      helpdesk_reports
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @help_desk_related_info
    end

    begin
      help_tickets
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @help_desk_related_info
    end

    begin
      helpdesk_custom_surveys
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @help_desk_related_info
    end

    begin
      scheduled_tasks
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @help_desk_related_info
    end

    # begin
    #   helpdesk_automated_tasks
    #   sample_company.save!
    # rescue => e
    #   @errors << e.message
    #   @errors_details << @help_desk_related_info
    # end

    begin
      company_builds
      sample_company.save!
    rescue => e
      @errors << e.message
      @errors_details << @company_builds_related_info
    end

    CreateSampleCompanyFifthWorker.perform_async unless this_worker_only
    raise (@errors + @errors_details).join(", ") if @errors.any?
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end

  def delete_data
    sample_company.help_tickets&.destroy_all
    sample_company.ticket_emails&.destroy_all
    sample_company.library_documents&.destroy_all
    sample_company.snippets&.destroy_all
    sample_company.articles.destroy_all
    sample_company.helpdesk_faqs&.destroy_all
    sample_company.scheduled_tasks&.destroy_all
    sample_company.company_builds&.destroy_all
    sample_company.helpdesk_settings&.destroy_all
    sample_company.ticket_list_columns&.destroy_all
  rescue => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end

  def help_tickets
    parent_company.help_tickets.each do |help_ticket|
      ht = help_ticket.dup
      @help_desk_related_info = { help_ticket_id: help_ticket.id }

      help_ticket.ticket_emails.each do |ticket_email|
        email = ticket_email.dup
        email.company_id = nil
        email.company = sample_company
        email.workspace = find_workspace(ticket_email.workspace)
        ht.ticket_emails << email
      end

      help_ticket.help_ticket_activities.each do |help_ticket_activity|
        activity = help_ticket_activity.dup
        activity.help_ticket = ht
        activity.owner = find_company_user(help_ticket_activity.owner&.guid)
        activity.created_at = help_ticket_activity.created_at
        ht.help_ticket_activities << activity
      end

      help_ticket_comments = {}
      help_ticket.help_ticket_comments.each do |comment|
        com = comment.dup
        com.help_ticket = ht
        com.contributor = find_company_user(comment.contributor&.company_user&.guid)&.contributor
        com.created_at = comment.created_at
        ht.help_ticket_comments << com
        help_ticket_comments[comment.id] = com
      end

      help_ticket.time_spents.each do |time_spent|
        ts = time_spent.dup
        ts.help_ticket = ht
        ts.company_user = find_company_user(time_spent.company_user&.guid)
        ts.created_at = time_spent.created_at
        ts.help_ticket_comment = help_ticket_comments[time_spent.help_ticket_comment.id] if time_spent.help_ticket_comment.present?
        ht.time_spents << ts
      end

      help_ticket.custom_form_values.each do |custom_value|
        form = sample_company.custom_forms.where(company_module: 'helpdesk').find_by(form_name: custom_value.custom_form.form_name)
        field = form.custom_form_fields.find_by(name: custom_value.custom_form_field.name)
        value = custom_value.dup

        case field.field_attribute_type
        when 'attachment'
          if custom_value.custom_form_attachment.present?
            attachment = custom_value.custom_form_attachment.dup
            attachment.company_id = sample_company.id
            value.custom_form_attachment = attachment
          end
        when 'rich_text'
          custom_value.attachment_uploads.each do |attachment_upload|
            attachment = attachment_upload.dup
            attachment.company_id = sample_company.id
            value.attachment_uploads << attachment
          end
        when 'people_list'
          contributor = Contributor.includes(:company_user, :group, :guest).find_by(id: custom_value.value_int)
          value.value_int = find_contributor(contributor&.contributor_staff(custom_value.custom_form_field.name.to_sym))&.contributor&.id
        when 'asset_list'
          asset_name = ManagedAsset.find_by_id(custom_value.value_int)&.name
          value.value_int = sample_company.managed_assets.find_by(name: asset_name)&.id
        when 'contract_list'
          contract_name = Contract.find_by_id(custom_value.value_int)&.name
          value.value_int = sample_company.contracts.find_by(name: contract_name)&.id
        when 'vendor_list'
          vendor_name = Vendor.find_by_id(custom_value.value_int)&.name
          value.value_int = find_vendor(vendor_name)&.id
        when 'telecom_list'
          telecom_name = TelecomService.find_by_id(custom_value.value_int)&.name
          value.value_int = sample_company.telecom_services.find_by(name: telecom_name)&.id
        when 'location_list'
          location_name = Location.find_by_id(custom_value.value_int)&.name
          value.value_int = find_location(location_name)&.id
        end

        value.custom_form = form
        value.custom_form_field = field
        value.help_ticket = ht
        value.company_id = sample_company.id
        ht.custom_form_values << value
      end

      ht.company_id = sample_company.id
      ht.workspace_id = find_workspace(help_ticket.workspace)&.id
      ht.guid = nil
      ht.created_at = help_ticket.created_at
      ht.custom_form = sample_company.custom_forms.where(company_module: 'helpdesk').find_by(form_name: help_ticket.custom_form.form_name)
      ht.save(validate: false)

      temp_project_tasks = {}
      help_ticket.project_tasks.each do |project_task|
        dup_project_task = project_task.dup
        dup_project_task.company_id = sample_company.id

        project_task.task_assignees.each do |assignee|
          dup_task_assignee = assignee.dup
          temp_comp_user = find_company_user(assignee.contributor&.company_user&.guid)
          if temp_comp_user.present? && temp_comp_user.contributor.present?
            dup_task_assignee.contributor_id = temp_comp_user.contributor.id
            dup_project_task.task_assignees << dup_task_assignee
          end
        end

        dup_project_task.save
        temp_project_tasks[project_task.id] = dup_project_task
      end

      temp_project_tasks.each do |key, value|
        if value.parent_project_task_id.present?
          value.parent_project_task_id = temp_project_tasks[value.parent_project_task_id].id
        end
        ht.project_tasks << value
      end
      ht.save(validate: false)

    end
    sample_company.save!
  end

  def ticket_emails
    parent_company.ticket_emails.each do |ticket_email|
      @help_desk_related_info = { ticket_email_id: ticket_email.id }
      email = ticket_email.dup
      email.company = sample_company
      email.workspace = find_workspace(ticket_email.workspace)
      email.message_id = SecureRandom.hex(16)
      sample_company.ticket_emails << email
    end
    sample_company.save!
  end

  def help_ticket_library_documents
    parent_company.library_documents.each do |library_document|
      @help_desk_related_info = { library_document_id: library_document.id }
      document = library_document.dup
      document.company = sample_company
      document.workspace = find_workspace(library_document.workspace)
      
      if library_document.attached_file.attached?
        file_blob = library_document.attached_file.blob
        if file_blob.service.exist?(file_blob.key)
          downloaded_library_file = file_blob.download
          document.attached_file.attach(
            io: StringIO.new(downloaded_library_file),
            filename: file_blob.filename.to_s,
            content_type: file_blob.content_type
          )
        end
      end

      sample_company.library_documents << document
    end
  end

  def snippets
    parent_company.snippets.each do |snippet|
      @help_desk_related_info = { snippet_id: snippet.id }
      new_snippet = snippet.dup
      new_snippet.workspace = find_workspace(snippet.workspace)
      new_snippet.company_id = sample_company.id
      sample_company.snippets << new_snippet
    end
  end

  def articles
    parent_company.articles.each do |article|
      @help_desk_related_info = { article_id: article.id }
      article_dup = article.dup
      article_dup.company_id = sample_company.id
      article_dup.workspace_id = find_workspace(article.workspace)&.id
      article_dup.company_user_id = admin_company_user.id
      article_category = article.category.instance_of?(String) ? article.category : article.category.name
      article_dup.category = sample_company.categories.find_by(name: article_category)&.name if article_category.present?
      attach_article_documents(article_dup, article) if article.article_documents.present?
      sample_company.articles << article_dup
    end
  end

  def attach_article_documents(duplicate_article, original_article)
    document_ids = original_article.article_documents.pluck(:library_document_id)
    parent_company_document_names = parent_company.library_documents.where(id: document_ids).pluck(:name)
    parent_company_document_names.each do |name|
      document_id = find_library_document(name)&.id
      duplicate_article.article_documents.find_or_initialize_by(library_document_id: document_id) if document_id.present?
    end
  end

  def helpdesk_faqs
    parent_company.helpdesk_faqs.each do |faq|
      @help_desk_related_info = { faq_id: faq.id }
      faq_dup = faq.dup
      faq_dup.company_id = sample_company.id
      faq_dup.workspace = find_workspace(faq.workspace)
      faq_dup.category = sample_company.helpdesk_categories.find_by(name: faq.category.name) if faq.category.present?
      sample_company.helpdesk_faqs << faq_dup
    end
  end

  def helpdesk_reports
    parent_company.workspaces.each do |workspace|
      next unless workspace.analytics_report_templates.present?
        workspace.analytics_report_templates.each do |report|
          @help_desk_related_info = { report_id: report.id }
          report_dup = report.dup
          report_dup.workspace = find_workspace(report.workspace)
          report_dup.save

          if report.scheduled_reports.present?
            report.scheduled_reports.each do |sc_report|
              scheduled_report_dup = sc_report.dup
              scheduled_report_dup.analytics_report_template_id = report_dup.id
              scheduled_report_dup.save
            end
          end
        end
    end
  end

  def helpdesk_automated_tasks
    parent_company.workspaces.each do |workspace|
      next unless workspace.automated_tasks.present?
  
      workspace.automated_tasks.each do |task|
        @automated_task_related_info = { task_id: task.id }
        task_dup = task.dup
        task_dup.workspace = find_workspace(workspace)
        task_dup.save(validate: false)
  
        task.scheduled_automated_tasks.each do |scheduled|
          scheduled_dup = scheduled.dup
          task_dup.scheduled_automated_tasks << scheduled_dup
        end
  
        if task.custom_form
          custom_form_dup = task.custom_form.dup
          task_dup.custom_form = custom_form_dup
          custom_form_dup.save
        end
  
        task.task_events.each do |event|
          event_dup = event.dup
          event_dup.event_type = event.event_type
          event_dup.automated_task_id = task_dup.id
          task_dup.task_events << event_dup
          event.event_details.each do |detail|
            detail_dup = detail.dup
            detail_dup.task_event_id = event_dup.id
            detail_dup.save
          end
        end
  
        task.task_actions.each do |action|
          action_dup = action.dup
          action_dup.automated_task_id = task_dup.id
          action_dup.action_type = action.action_type
          task_dup.task_actions << action_dup
          if action.task_next_assignment
            assignment_dup = action.task_next_assignment.dup
            assignment_dup.task_action_id = action_dup.id
            assignment_dup.save
          end
        end
  
        task.task_criteria.each do |criteria|
          criteria_dup = criteria.dup
          task_dup.task_criteria << criteria_dup
        end
  
        task.execution_dates.each do |date|
          date_dup = date.dup
          task_dup.execution_dates << date_dup
        end
  
        task.execution_logs.each do |log|
          log_dup = log.dup
          task_dup.execution_logs << log_dup
        end
      end
    end
  end

  def helpdesk_settings
    parent_company.helpdesk_settings.each do |setting|
      @help_desk_related_info = { setting_id: setting.id }
      help_desk_settings = setting.dup
      help_desk_settings.workspace = find_workspace(setting.workspace)
      sample_company.helpdesk_settings << help_desk_settings
    end
  end
  
  def helpdesk_custom_surveys
    parent_company.workspaces.each do |workspace|
      @help_desk_related_info = { workspace_id: workspace.id }
      sample_workspace = find_workspace(workspace)
      next unless workspace.custom_surveys.present? && sample_workspace

      workspace.custom_surveys.each do |survey|
        survey_dup = survey.dup
        survey_dup.workspace = sample_workspace
        survey_dup.company = sample_company
        survey_dup.save(validate: false)

        if survey.trigger
          trigger_dup = survey.trigger.dup
          trigger_dup.custom_survey_id = survey_dup.id
          trigger_dup.save
        end

        survey.questions.each do |question|
          question_dup = question.dup
          question_dup.custom_survey_id = survey_dup.id
          question_dup.save

          question.choices.each do |choice|
            choice_dup = choice.dup
            choice_dup.custom_survey_question_id = question_dup.id
            choice_dup.save
          end
        end

        survey.rules.each do |rule|
          rule_dup = rule.dup
          rule_dup.custom_survey_id = survey_dup.id
          rule_dup.save

          rule.actions.each do |action|
            action_dup = action.dup
            action_dup.custom_survey_rule_id = rule_dup.id
            action_dup.save
          end
        end

        survey.responses.each do |response|
          response_dup = response.dup
          response_dup.custom_survey_id = survey_dup.id
          response_dup.company = sample_company
          response_dup.workspace = sample_workspace
          response_dup.contributor = find_contributor(response.contributor.contributor_staff)&.contributor if response.contributor
          response_dup.save
        end
      end
    end
  end

  def ticket_list_columns
    parent_company.workspaces.each do |workspace|
      workspace_preference = workspace.ticket_list_column
      if workspace_preference.present?
        @help_desk_related_info = { ticket_list_column_id: workspace_preference.id }

        clone_workspace = find_workspace(workspace)
        clone_workspace.create_default_ticket_list_columns if clone_workspace.ticket_list_column.nil?
        clone_workspace.ticket_list_column.update_columns(columns: workspace_preference.columns) if clone_workspace.present?
      end
    end
  end

  def scheduled_tasks
    parent_company.scheduled_tasks.each do |scheduled_task|
      @help_desk_related_info = { scheduled_task_id: scheduled_task.id }
      new_scheduled_task = scheduled_task.dup
      if scheduled_task.assignee_id.present?
        parent_assignee = Contributor.find_by(id: scheduled_task.assignee_id)
        if parent_assignee.group
          grp = sample_company.groups.find_by(name: parent_assignee.name)
          new_assignee_id = grp&.contributor_id
        else
          parent_assignee_email = parent_assignee.email
          sample_cu = company_user_cache(sample_company, parent_assignee_email)
          new_assignee_id = sample_cu&.contributor_id
        end
        
        new_scheduled_task.assignee_id = new_assignee_id
      end
      new_scheduled_task.company_id = sample_company.id
      new_scheduled_task.workspace = find_workspace(scheduled_task.workspace)
      
      scheduled_task.scheduled_task_notifications.each do |notification|
        new_notification = notification.dup
        new_notification.scheduled_task_id = new_scheduled_task.id
        new_scheduled_task.scheduled_task_notifications << new_notification
      end

      if scheduled_task.scheduled_task_recurrence.present?
        new_recurrence = scheduled_task.scheduled_task_recurrence.dup
        new_recurrence.scheduled_task_id = new_scheduled_task.id
        new_scheduled_task.scheduled_task_recurrence = new_recurrence
      end
      new_scheduled_task.save(validate: false)
    end
  end

  def company_builds
    parent_company.company_builds.each do |build|
      @company_builds_related_info = { build_id: build.id }
      new_build = build.dup
      new_build.company_id = sample_company.id
      if build.build_data_set
        duplicated_build_data_set = build.build_data_set.dup  
        new_build.build_data_set = duplicated_build_data_set
      end
      sample_company.company_builds << new_build
    end
  end
end
